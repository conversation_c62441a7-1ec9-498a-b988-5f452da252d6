<template>
  <el-drawer 
    :title="form.id ? '编辑题目' : '新增题目'" 
    v-model="dialogVisible" 
    size="800px" 
    :close-on-click-modal="false"
    :before-close="handleClose" 
    direction="rtl"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" class="drawer-form">
      <el-form-item label="题目标题" prop="title">
        <el-input 
          v-model="form.title" 
          placeholder="请输入题目标题" 
          type="textarea"
          :rows="3"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="所属分类" prop="categoryId">
        <el-select v-model="form.categoryId" placeholder="请选择分类" style="width: 100%;" clearable @change="handleCategoryChange">
          <el-option v-for="item in props.categoryOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="所属题库" prop="bankId">
        <el-select v-model="form.bankId" placeholder="请选择题库" style="width: 100%;" clearable>
          <el-option v-for="item in currentBankOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="题目类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择题目类型" style="width: 100%;" @change="handleTypeChange">
          <el-option label="单选题" value="SINGLE" />
          <el-option label="多选题" value="MULTIPLE" />
        </el-select>
      </el-form-item>

      <el-form-item label="难度" prop="difficulty">
        <el-select v-model="form.difficulty" placeholder="请选择难度" style="width: 100%;">
          <el-option label="简单" :value="1" />
          <el-option label="一般" :value="2" />
          <el-option label="困难" :value="3" />
        </el-select>
      </el-form-item>

      <!-- <el-form-item label="分值" prop="score">
        <el-input-number v-model="form.score" :min="1" :max="100" placeholder="请输入分值" style="width: 100%;" />
      </el-form-item> -->

      <!-- 选项管理 -->
      <el-form-item label="选项" prop="options">
        <div class="options-container">
          <div class="options-header">
            <span>选项设置</span>
            <el-button type="primary" plain size="small" @click="addOption" :disabled="form.options.length >= 6">
              添加选项
            </el-button>
          </div>
          <div v-for="(option, index) in form.options" :key="index" class="option-item">
            <span class="option-label">选项 {{ String.fromCharCode(65 + index) }}</span>
            <el-input
              v-model="option.content"
              placeholder="请输入选项内容"
              maxlength="200"
              show-word-limit
              class="option-input"
            />
            <el-button
              type="danger"
              size="small"
              link
              @click="removeOption(index)"
              :disabled="form.options.length <= 2"
              class="option-delete-btn"
            >
              删除
            </el-button>
          </div>
        </div>
      </el-form-item>

      <!-- 正确答案 -->
      <el-form-item label="正确答案" prop="answer">
        <div v-if="form.type === 'SINGLE'">
          <el-radio-group v-model="form.answer">
            <el-radio
              v-for="(_, index) in form.options"
              :key="index"
              :label="String.fromCharCode(65 + index)"
              class="answer-option-item"
            >
              选项 {{ String.fromCharCode(65 + index) }}
            </el-radio>
          </el-radio-group>
        </div>
        <div v-else-if="form.type === 'MULTIPLE'">
          <el-checkbox-group v-model="form.answerArray">
            <el-checkbox
              v-for="(_, index) in form.options"
              :key="index"
              :label="String.fromCharCode(65 + index)"
              class="answer-option-item"
            >
              选项 {{ String.fromCharCode(65 + index) }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
        <div v-else>
          <el-text type="info">请先选择题目类型</el-text>
        </div>
      </el-form-item>

      <el-form-item label="解析" prop="analysis">
        <el-input 
          v-model="form.analysis" 
          placeholder="请输入题目解析（可选）" 
          type="textarea"
          :rows="4"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <div class="drawer-footer">
      <el-button @click="handleClose" :disabled="isSubmitting">取 消</el-button>
      <el-button type="primary" @click="submitForm" :loading="isSubmitting">确 定</el-button>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import API from '@/api/maintainance'
import { validateNoWhitespace } from '@/utils/validateRule'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  },
  categoryOptions: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:visible', 'success'])

const dialogVisible = ref(false)
const formRef = ref()
const isSubmitting = ref(false)

// 表单数据
const form = reactive({
  id: '',
  title: '',
  categoryId: '',
  bankId: '',
  type: '',
  difficulty: '',
  // score: 1,
  options: [],
  answer: '',
  answerArray: [],
  analysis: ''
})

// 当前题库选项（根据分类筛选）
const currentBankOptions = ref([])

// 表单验证规则
const rules = reactive({
  title: [
    { required: true, message: '请输入题目标题', trigger: 'blur' },
    { max: 500, message: '题目标题不能超过500个字符', trigger: 'blur' },
    { validator: validateNoWhitespace, trigger: 'blur' }
  ],
  categoryId: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  bankId: [
    { required: true, message: '请选择题库', trigger: 'change' }
  ],
  type: [
    { required: true, message: '请选择题目类型', trigger: 'change' }
  ],
  difficulty: [
    { required: true, message: '请选择难度', trigger: 'change' }
  ],
  // score: [
  //   { required: true, message: '请输入分值', trigger: 'blur' }
  // ],
  options: [
    {
      validator: (_, value, callback) => {
        if (!value || value.length < 2) {
          callback(new Error('至少需要2个选项'));
          return;
        }
        const hasEmptyOption = value.some(option => !option.content || !option.content.trim());
        if (hasEmptyOption) {
          callback(new Error('请填写所有选项内容'));
          return;
        }
        callback();
      },
      trigger: 'blur'
    }
  ],
  answer: [
    { required: true, message: '请选择正确答案', trigger: 'change' }
  ]
})

// 初始化选项
const initOptions = () => {
  form.options = [
    { content: '', optionKey: 'A', sortNo: 1 },
    { content: '', optionKey: 'B', sortNo: 2 }
  ];
};

// 添加选项
const addOption = () => {
  if (form.options.length < 6) {
    const nextIndex = form.options.length;
    form.options.push({
      content: '',
      optionKey: String.fromCharCode(65 + nextIndex),
      sortNo: nextIndex + 1
    });
  }
};

// 删除选项
const removeOption = (index) => {
  if (form.options.length > 2) {
    const removedLabel = String.fromCharCode(65 + index);
    form.options.splice(index, 1);

    // 重新调整所有选项的 optionKey 和 sortNo
    form.options.forEach((option, idx) => {
      option.optionKey = String.fromCharCode(65 + idx);
      option.sortNo = idx + 1;
    });

    // 重新调整答案
    if (form.type === 'SINGLE') {
      if (form.answer === removedLabel) {
        form.answer = '';
      }
    } else if (form.type === 'MULTIPLE') {
      const answerIndex = form.answerArray.indexOf(removedLabel);
      if (answerIndex > -1) {
        form.answerArray.splice(answerIndex, 1);
      }
    }
  }
};

// 获取题库列表
const getBankList = async (categoryId) => {
  try {
    const params = { categoryId };
    const res = await API.getQuestionBankListByCategory(params);
    if (res.success && res.result) {
      currentBankOptions.value = res.result.map(item => ({
        label: item.name,
        value: item.id
      }));
    }
  } catch (error) {
    console.error('获取题库列表失败:', error);
    currentBankOptions.value = [];
  }
};

// 分类变化处理
const handleCategoryChange = (categoryId) => {
  // 清空题库选择
  form.bankId = '';
  // 根据分类动态请求题库选项
  if (categoryId) {
    getBankList(categoryId);
  } else {
    currentBankOptions.value = [];
  }
};

// 题目类型变化处理
const handleTypeChange = () => {
  // 只重置答案，不影响选项
  form.answer = '';
  form.answerArray = [];
};

// 监听弹窗显示状态
watch(() => props.visible, (val) => {
  dialogVisible.value = val
  if (val) {
    if (props.data.id) {
      // 编辑模式，填充表单数据
      Object.assign(form, {
        id: props.data.id,
        title: props.data.title || '',
        categoryId: props.data.categoryId || '',
        bankId: props.data.bankId || '',
        type: props.data.type || '',
        difficulty: props.data.difficulty || '',
        // score: props.data.score || 1,
        options: [],
        answer: props.data.answer || '',
        answerArray: props.data.type === 'MULTIPLE' ? (props.data.answer ? props.data.answer.split('') : []) : [],
        analysis: props.data.analysis || ''
      })

      // 根据分类设置题库选项
      if (form.categoryId) {
        handleCategoryChange(form.categoryId);
      }

      // 处理选项数据，确保格式正确
      if (props.data.options && props.data.options.length > 0) {
        form.options = props.data.options.map((option, index) => ({
          content: option.content || '',
          optionKey: option.optionKey || String.fromCharCode(65 + index),
          sortNo: option.sortNo || (index + 1)
        }));
      } else {
        initOptions();
      }
    } else {
      // 新增模式，重置表单
      resetForm()
      // 新增模式下初始化选项
      initOptions()
    }
  }
}, { immediate: true })

// 监听内部弹窗状态变化
watch(dialogVisible, (val) => {
  emit('update:visible', val)
})

// 监听多选答案变化
watch(() => form.answerArray, (newVal) => {
  if (form.type === 'MULTIPLE') {
    form.answer = newVal.join('');
  }
}, { deep: true })

// 提交表单
const submitForm = async () => {
  if (isSubmitting.value) return
  
  try {
    await formRef.value.validate()
    
    // 验证选项内容
    const hasEmptyOption = form.options.some(option => !option.content.trim());
    if (hasEmptyOption) {
      ElMessage.warning('请填写所有选项内容');
      return;
    }
    
    // 验证答案
    if (form.type === 'MULTIPLE' && form.answerArray.length === 0) {
      ElMessage.warning('多选题至少选择一个正确答案');
      return;
    }
    
    isSubmitting.value = true

    const apiMethod = form.id ? API.editQuestion : API.addQuestion
    const params = {
      title: form.title.trim(),
      categoryId: form.categoryId,
      bankId: form.bankId,
      type: form.type,
      difficulty: form.difficulty,
      // score: form.score,
      options: form.options.map((option, index) => ({
        optionKey: String.fromCharCode(65 + index),
        content: option.content.trim(),
        sortNo: index + 1
      })),
      answer: form.answer,
      analysis: form.analysis.trim()
    }
    
    if (form.id) {
      params.id = form.id
    }

    const res = await apiMethod(params)

    if (res.data.success) {
      ElMessage.success('操作成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(res.data.error || '操作失败')
    }
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.warning('请完善表单信息')
  } finally {
    isSubmitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    id: '',
    title: '',
    categoryId: '',
    bankId: '',
    type: '',
    difficulty: '',
    // score: 1,
    options: [],
    answer: '',
    answerArray: [],
    analysis: ''
  })
  // 清空题库选项
  currentBankOptions.value = [];
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
  formRef.value?.resetFields()
  resetForm()
}


</script>

<style lang="less" scoped>
.drawer-form {
  // padding: 20px;
  padding-bottom: 80px;
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
  background: #fff;
  text-align: right;
}

:deep(.el-drawer__body) {
  padding-bottom: 80px;
}

.options-container {
  width: 100%;

  .options-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e4e7ed;

    span {
      font-weight: 500;
      color: #303133;
    }
  }

  .option-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    gap: 12px;

    .option-label {
      font-weight: 500;
      color: #606266;
      min-width: 60px;
      flex-shrink: 0;
    }

    .option-input {
      flex: 1;
    }

    .option-delete-btn {
      flex-shrink: 0;
    }
  }
}
</style>
