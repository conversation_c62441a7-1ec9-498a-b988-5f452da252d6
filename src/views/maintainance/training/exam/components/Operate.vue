<template>
  <el-drawer 
    :title="form.id ? '编辑试卷' : '新增试卷'" 
    v-model="dialogVisible" 
    size="800px" 
    :close-on-click-modal="false"
    :before-close="handleClose"
    direction="rtl"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" class="drawer-form">
      <el-form-item label="试卷名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入试卷名称"
          maxlength="100"
          show-word-limit
          :disabled="form.status === 'PUBLISHED' || form.status === 'ENDED'"
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="开始时间" prop="startTime">
            <el-date-picker
              v-model="form.startTime"
              type="datetime"
              placeholder="选择开始时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%;"
              :disabled="form.status === 'PUBLISHED' || form.status === 'ENDED'"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束时间" prop="endTime">
            <el-date-picker
              v-model="form.endTime"
              type="datetime"
              placeholder="选择结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%;"
              :disabled="form.status === 'PUBLISHED' || form.status === 'ENDED'"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="考试时长" prop="duration">
        <el-input-number
          v-model="form.duration"
          :min="1"
          :max="999"
          placeholder="分钟"
          style="width: 100%;"
          :disabled="form.status === 'PUBLISHED' || form.status === 'ENDED'"
        />
      </el-form-item>

      <el-form-item label="参与角色" prop="roleCodes">
        <el-select
          v-model="form.roleCodes"
          multiple
          filterable
          placeholder="请选择角色（可多选）"
          style="width: 100%;"
          :disabled="form.status === 'PUBLISHED' || form.status === 'ENDED'"
        >
          <el-option v-for="item in roleOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item v-if="isShowSubCenter" label="参与分中心" prop="subCenterCodes">
        <el-select
          v-model="form.subCenterCodes"
          multiple
          placeholder="请选择分中心（可多选）"
          style="width: 100%;"
          :disabled="form.status === 'PUBLISHED' || form.status === 'ENDED'"
        >
          <el-option v-for="item in subCenterOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="考试题库" prop="bankRels">
        <div style="margin-bottom: 10px;">
          <el-button
            type="primary"
            size="small"
            @click="addBankRel"
            :disabled="form.status === 'PUBLISHED' || form.status === 'ENDED'"
          >
            添加题库
          </el-button>
        </div>

        <el-table :data="form.bankRels" border style="width: 100%;">
          <el-table-column label="题库名称" min-width="200">
            <template #default="scope">
              <el-select
                v-model="scope.row.bankId"
                placeholder="选择题库"
                style="width: 100%;"
                filterable
                clearable
                @change="handleBankChange(scope.$index, scope.row.bankId)"
                :disabled="form.status === 'PUBLISHED' || form.status === 'ENDED'"
              >
                <el-option
                  v-for="item in getAvailableBankOptions(scope.$index)"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="单选题数量" width="120">
            <template #default="scope">
              <span>{{ scope.row.availableSingleCount || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="多选题数量" width="120">
            <template #default="scope">
              <span>{{ scope.row.availableMultipleCount || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="抽取数量" width="120">
            <template #default="scope">
              <el-input-number
                v-model="scope.row.extractCount"
                :min="0"
                :max="999"
                size="small"
                style="width: 100%;"
                :disabled="form.status === 'PUBLISHED' || form.status === 'ENDED'"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80" v-if="form.status !== 'PUBLISHED' && form.status !== 'ENDED'">
            <template #default="scope">
              <el-button
                link
                type="danger"
                size="small"
                @click="removeBankRel(scope.$index)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
    </el-form>

    <div class="drawer-footer">
      <el-button @click="handleClose" :disabled="isSubmitting">取 消</el-button>
      <el-button type="primary" @click="submitForm" :loading="isSubmitting">确 定</el-button>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, reactive, watch, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import API from '@/api/maintainance'
import { validateNoWhitespace } from '@/utils/validateRule'
import { useDictStore } from '@/stores/modules/dict'
import _D from '@/edata/_osp_data'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

const dialogVisible = ref(false)
const formRef = ref()
const isSubmitting = ref(false)
const dictStore = useDictStore()

// 表单数据
const form = reactive({
  id: '',
  name: '',
  startTime: '',
  endTime: '',
  duration: 60,
  passScore: 60,
  totalScore: 100,
  roleCodes: [],
  subCenterCodes: [],
  bankRels: [],
  status: 'DRAFT'
})

// 选项数据
const roleOptions = computed(() => dictStore.getDictByType('user_role'))
const subCenterOptions = _D.subCenterList
const bankOptions = ref([])

// 是否显示分中心选择
const isShowSubCenter = computed(() => {
  return !(
    (form.roleCodes.includes("head_office") &&
      form.roleCodes.length === 1) ||
    form.roleCodes.length === 0
  )
})

// 表单验证规则
const rules = reactive({
  name: [
    { required: true, message: '请输入试卷名称', trigger: 'blur' },
    { max: 100, message: '试卷名称不能超过100个字符', trigger: 'blur' },
    { validator: validateNoWhitespace, trigger: 'blur' }
  ],
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  endTime: [
    { required: true, message: '请选择结束时间', trigger: 'change' }
  ],
  duration: [
    { required: true, message: '请输入考试时长', trigger: 'blur' }
  ],
  bankRels: [
    {
      validator: (_, value, callback) => {
        if (!value || value.length === 0) {
          callback(new Error('请至少添加一个题库'))
          return
        }

        // 检查是否所有题库都已选择
        const hasEmptyBank = value.some(item => !item.bankId)
        if (hasEmptyBank) {
          callback(new Error('请选择所有题库'))
          return
        }

        // 检查抽取数量总和是否至少为20
        const totalExtractCount = value.reduce((sum, item) => sum + (item.extractCount || 0), 0)
        if (totalExtractCount < 20) {
          callback(new Error('抽取数量总和至少为20'))
          return
        }

        callback()
      },
      trigger: 'change'
    }
  ]
  // passScore: [
  //   { required: true, message: '请输入及格分数', trigger: 'blur' }
  // ],
  // totalScore: [
  //   { required: true, message: '请输入总分数', trigger: 'blur' }
  // ]
})

// 获取题库列表
const getBankOptions = async () => {
  try {
    const res = await API.getQuestionBankPage({ pageNum: 1, pageSize: 1000 })
    if (res.success && res.result && res.result.content) {
      bankOptions.value = res.result.content.map(item => ({
        label: item.name,
        value: item.id
      }))
    }
  } catch (error) {
    console.error('获取题库列表失败:', error)
  }
}

// 获取可用的题库选项（过滤已选择的题库）
const getAvailableBankOptions = (currentIndex) => {
  const selectedBankIds = form.bankRels
    .map((item, index) => index !== currentIndex ? item.bankId : null)
    .filter(id => id)

  return bankOptions.value.filter(option => !selectedBankIds.includes(option.value))
}

// 添加题库关系
const addBankRel = () => {
  form.bankRels.push({
    bankId: '',
    bankName: '',
    singleChoiceCount: 0,
    multipleChoiceCount: 0,
    extractCount: 0,
    availableSingleCount: 0,
    availableMultipleCount: 0
  })
}

// 删除题库关系
const removeBankRel = (index) => {
  form.bankRels.splice(index, 1)
}

// 题库选择变化处理
const handleBankChange = async (index, bankId) => {
  const bank = bankOptions.value.find(item => item.value === bankId)
  if (bank) {
    form.bankRels[index].bankName = bank.label

    // 获取题库详情，包含题目数量信息
    try {
      const res = await API.getQuestionBank({ id: bankId })
      if (res.success && res.result) {
        form.bankRels[index].availableSingleCount = res.result.singleChoiceCount || 0
        form.bankRels[index].availableMultipleCount = res.result.multipleChoiceCount || 0
      }
    } catch (error) {
      console.error('获取题库详情失败:', error)
      form.bankRels[index].availableSingleCount = 0
      form.bankRels[index].availableMultipleCount = 0
    }
  }
}

// 监听弹窗显示状态
watch(() => props.visible, (val) => {
  dialogVisible.value = val
  if (val) {
    if (props.data.id) {
      // 编辑模式，填充表单数据
      Object.assign(form, {
        id: props.data.id,
        name: props.data.name || '',
        startTime: props.data.startTime || '',
        endTime: props.data.endTime || '',
        duration: props.data.duration || 60,
        passScore: props.data.passScore || 60,
        totalScore: props.data.totalScore || 100,
        roleCodes: props.data.roleCodes || [],
        subCenterCodes: props.data.subCenterCodes || [],
        bankRels: props.data.bankRels || [],
        status: props.data.status || 'DRAFT'
      })
    } else {
      // 新增模式，重置表单
      resetForm()
    }
  }
}, { immediate: true })

// 监听内部弹窗状态变化
watch(dialogVisible, (val) => {
  emit('update:visible', val)
})

// 提交表单
const submitForm = async () => {
  if (isSubmitting.value) return
  
  try {
    await formRef.value.validate()

    isSubmitting.value = true

    const apiMethod = form.id ? API.editExam : API.addExam
    const params = {
      name: form.name.trim(),
      startTime: form.startTime,
      endTime: form.endTime,
      duration: form.duration,
      passScore: form.passScore,
      totalScore: form.totalScore,
      roleCodes: form.roleCodes,
      subCenterCodes: form.subCenterCodes,
      bankRels: form.bankRels.map(item => ({
        bankId: item.bankId,
        singleChoiceCount: item.singleChoiceCount || 0,
        multipleChoiceCount: item.multipleChoiceCount || 0,
        extractCount: item.extractCount || 0
      }))
    }
    
    if (form.id) {
      params.id = form.id
    }

    const res = await apiMethod(params)

    if (res.data.success) {
      ElMessage.success('操作成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(res.data.error || '操作失败')
    }
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.warning('请完善表单信息')
  } finally {
    isSubmitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    id: '',
    name: '',
    startTime: '',
    endTime: '',
    duration: 60,
    passScore: 60,
    totalScore: 100,
    roleCodes: [],
    subCenterCodes: [],
    bankRels: [],
    status: 'DRAFT'
  })
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
  formRef.value?.resetFields()
  resetForm()
}

// 组件挂载时获取数据
onMounted(async () => {
  await dictStore.fetchDict(['user_role'])
  await getBankOptions()
})
</script>

<style lang="less" scoped>
.drawer-form {
  height: calc(100% - 80px);
  overflow-y: auto;
  padding-right: 12px;
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px 20px;
  background-color: #fff;
  border-top: 1px solid #e4e7ed;
  text-align: right;

  .el-button {
    margin-left: 8px;
  }
}
</style>
