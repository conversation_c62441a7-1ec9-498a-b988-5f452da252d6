<template>
  <div class="fault-detail-page">
    <el-page-header class="cus-back" icon="ArrowLeft" @back="$router.go(-1)" />
    <!-- 故障工单信息 -->
    <el-card class="section-card">
      <template #header>
        <div class="card-header">运维工单</div>
      </template>
      <el-descriptions :column="3" border>
        <el-descriptions-item label="运维单号">{{ formData.orderCode || '-' }}</el-descriptions-item>
        <el-descriptions-item label="工单状态">{{ getDictLabel('work_order_status', formData.orderStatus) || '-'
          }}</el-descriptions-item>
        <el-descriptions-item label="工单类型">{{ getDictLabel('work_order_type',
          formData.orderType) }}</el-descriptions-item>
        <el-descriptions-item label="工单来源">{{ getDictLabel('work_order_source',
          formData.orderSource) }}</el-descriptions-item>
        <el-descriptions-item label="截止时间">{{ formData.deadline || '-' }}</el-descriptions-item>
        <el-descriptions-item label="下发方式">{{ getDictLabel('dispatch_mode',
          formData.dispatchMode) }}</el-descriptions-item>
        <el-descriptions-item label="审核权限">{{ getDictLabel('dispatch_review_permission',
          formData.dispatchAuditPermission) }}</el-descriptions-item>
        <el-descriptions-item label="审核方式">{{ getDictLabel('review_mode', formData.auditMode) }}</el-descriptions-item>
        <el-descriptions-item label="是否超时">{{ typeof formData.overTime === 'boolean' ? (formData.overTime ? '是' : '否') :
          '-'
        }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formData.createdAt || '-' }}</el-descriptions-item>
        <el-descriptions-item label="创建人">{{ formData.createdBy || '-' }}</el-descriptions-item>
        <!-- <el-descriptions-item label="备注" :span="3">{{ formData.remark || '-' }}</el-descriptions-item> -->
      </el-descriptions>
    </el-card>

    <!-- 电站信息 -->
    <el-card class="section-card">
      <template #header>
        <div class="card-header">
          <span>电站信息</span>
        </div>
      </template>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="电站编码">{{ formData.stationCode || '-' }}</el-descriptions-item>
        <el-descriptions-item label="逆变器SN码">
          <el-button v-if="formData.inverterSn" link type="primary">
            {{ formData.inverterSn }}
          </el-button>
        </el-descriptions-item>
        <el-descriptions-item label="业主姓名">{{ formData.stationName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ formData.stationPhone || '-' }}</el-descriptions-item>
        <el-descriptions-item label="电站模式">{{ formData.stationMode || '-' }}</el-descriptions-item>
        <el-descriptions-item label="关联资方">{{ pmSpecialFlag[formData.specialFlag] || '-' }}</el-descriptions-item>
        <el-descriptions-item label="所属分中心">{{ formData.subCenterName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="服务商类别">{{ identityType[formData.opType] || '-' }}</el-descriptions-item>
        <el-descriptions-item label="运维商名称">{{ formData.opName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="运维商业务类型">{{ businessType[formData.businessType] || '-' }}</el-descriptions-item>
        <el-descriptions-item label="是否质保期内">{{ isWarranty[formData.isWarranty] || '-' }}</el-descriptions-item>
        <el-descriptions-item label="区域">{{ (formData.provinceName || '') + (formData.cityName || '') +
          (formData.regionName
            || '') || '-' }}</el-descriptions-item>
        <el-descriptions-item label="详细地址">{{ formData.address || '-' }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 故障信息 -->
    <el-card class="section-card">
      <template #header>
        <div class="card-header">故障信息</div>
      </template>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="故障名称">{{ formData.faultInfo?.faultName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="故障等级">{{ getDictLabel('fault_level', formData.faultInfo?.faultLevel)
          }}</el-descriptions-item>
        <el-descriptions-item label="故障描述">{{ formData.faultInfo?.faultDetails || '-' }}</el-descriptions-item>
        <el-descriptions-item label="故障照片">
          <template v-if="formData.faultInfo?.photos">
            <el-image v-for="(photo, index) in formData.faultInfo.photos.split(',')" :key="index"
              style="width: 100px; height: 100px; margin-right: 10px;" :src="photo"
              :preview-src-list="formData.faultInfo.photos.split(',')" :initial-index="index" fit="cover"
              preview-teleported />
          </template>
          <span v-else>-</span>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 流程信息 和 工单处理 Tab -->
    <el-tabs v-model="activeTabName" type="border-card" class="section-card">
      <el-tab-pane
        v-if="!['TO_HEAD_DISPATCH', 'TO_SUB_CENTER_DISPATCH', 'TO_ASSIGN', 'CLOSED'].includes(formData.orderStatus)"
        label="工单处理" name="workOrderHandle">
        <el-card>
          <el-form ref="formRef" :model="formData" label-position="top" :rules="rules" label-width="120px"
            class="form-container">
            <div v-for="(item, idx) in formData.handleCheckItems" :key="'cfg' + idx">
              <el-form-item v-if="item.resultType === 'text'" :label="item.checkItem"
                :prop="`handleCheckItems[${idx}].resultContent`"
                :rules="[{ required: true, message: `请输入${item.checkItem}`, trigger: 'blur' }]">
                <el-input v-model="item.resultContent" type="textarea" rows="3" :placeholder="`请输入${item.checkItem}`"
                  :disabled="!editable" />
              </el-form-item>

              <el-form-item v-if="item.resultType === 'select'" :label="item.checkItem"
                :prop="`handleCheckItems[${idx}].resultContent`"
                :rules="[{ required: true, message: `请选择${item.checkItem}`, trigger: 'change' }]">
                <el-select v-model="item.resultContent" :placeholder="`请选择${item.checkItem}`" :disabled="!editable">
                  <el-option key="良好" label="良好" value="良好">
                  </el-option>
                  <el-option key="损坏" label="损坏" value="损坏">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item v-if="item.resultType === 'image'" :label="item.checkItem"
                :prop="`handleCheckItems[${idx}].resultContent`" :rules="[{
                  required: true, message: `请上传${item.checkItem}照片`, trigger: 'change', validator: (rule, value, callback) => {
                    if (!value || (Array.isArray(value) && value.length === 0)) {
                      callback(new Error(`请上传${item.checkItem}照片`));
                    } else {
                      callback();
                    }
                  }
                }]">
                <el-row :gutter="20" style="width: 100%;">
                  <el-col :span="6">
                    <el-form-item label="示例照片">
                      <el-image style="width: 150px; height: 150px; background-color: #eee;" :src="item.exampleImage"
                        fit="cover">
                        <template #error>
                          <div class="image-slot">示例图</div>
                        </template>
                      </el-image>
                    </el-form-item>
                  </el-col>
                  <el-col :span="18"
                    v-if="editable || (!editable && item.resultContent && item.resultContent.length > 0)">
                    <el-form-item label="上传照片">
                      <HMultiUpload v-model="item.resultContent" :limit="3" list-type="picture-card"
                        :disabled="!editable" />
                    </el-form-item>
                  </el-col>
                  <!-- <el-col :span="9">
                    <el-form-item label="检修后照片">
                      <HMultiUpload v-model="item.afterRepairImages" :limit="1" list-type="picture-card"
                        :disabled="formData.orderStatus !== 'TO_PROCESS'" />
                    </el-form-item>
                  </el-col> -->
                </el-row>
              </el-form-item>
            </div>
            <el-form-item label="备注" prop="remark">
              <el-input v-model="formData.remark" type="textarea" rows="3" placeholder="请输入备注" :disabled="!editable" />
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>
      <el-tab-pane label="工单流程" name="processInfo">
        <el-card v-if="Array.isArray(formData.processes) && formData.processes.length > 0">
          <el-timeline>
            <el-timeline-item v-for="(process, index) in formData.processes" :key="index"
              :timestamp="process.createdAt">
              <div>
                <div>{{ process.processName }} - {{ process.createdBy }}</div>
                <div v-if="process.handleCheckItemsJson" class="handle-check-items">
                  <div class="check-items-title">处理检查项：</div>
                  <el-descriptions :column="1" border size="small">
                    <template v-for="(item, idx) in JSON.parse(process.handleCheckItemsJson)" :key="'check-'+idx">
                      <el-descriptions-item :label="item.checkItem">
                        <template v-if="item.resultType === 'text' || item.resultType === 'select'">
                          {{ item.resultContent || '-' }}
                        </template>
                        <template v-else-if="item.resultType === 'image' && item.resultContent">
                          <el-image v-for="(img, imgIdx) in item.resultContent.split(',')" :key="'img-' + imgIdx"
                            style="width: 80px; height: 80px; margin-right: 5px;" :src="img"
                            :preview-src-list="item.resultContent.split(',')" :initial-index="imgIdx" fit="cover"
                            preview-teleported />
                        </template>
                      </el-descriptions-item>
                    </template>
                  </el-descriptions>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </el-card>
        <el-empty v-else description="暂无工单流程" />
      </el-tab-pane>
    </el-tabs>

    <!-- 底部操作按钮 -->
    <div class="action-buttons" v-if="!['CLOSED', 'FINISHED'].includes(formData.orderStatus) && action !== 'view'">
      <el-button v-if="['TO_HEAD_DISPATCH', 'TO_SUB_CENTER_DISPATCH'].includes(formData.orderStatus)"
        @click="handleOpenCloseOrderDialog">关单</el-button>
      <el-button v-if="['TO_HEAD_DISPATCH', 'TO_SUB_CENTER_DISPATCH'].includes(formData.orderStatus)" type="success"
        @click="dispatchOrder">下发</el-button>
      <el-button v-if="['TO_PROCESS'].includes(formData.orderStatus)" type="success"
        @click="handleSubmit">提交</el-button>
      <el-button v-if="['TO_SUB_CENTER_AUDIT'].includes(formData.orderStatus) && isSubCenter === true" type="danger"
        @click="handleOpenRejectOrderDialog">驳回</el-button>
      <el-button v-if="['TO_SUB_CENTER_AUDIT'].includes(formData.orderStatus) && isSubCenter === true" type="success"
        @click="handleAuditPass">通过</el-button>
      <el-button v-if="['TO_HEAD_AUDIT'].includes(formData.orderStatus) && isSubCenter === false" type="danger"
        @click="handleOpenRejectOrderDialog">驳回</el-button>
      <el-button v-if="['TO_HEAD_AUDIT'].includes(formData.orderStatus) && isSubCenter === false" type="success"
        @click="handleAuditPass">通过</el-button>
    </div>

    <!-- 关单弹窗组件 -->
    <CloseOrderDialog v-model:visible="closeOrderDialogVisible" :orderCode="formData.orderCode"
      @closed="handleOrderClosed" />
    <RejectOrderDialog v-model:visible="rejectOrderDialogVisible" :orderCode="formData.orderCode"
      @rejected="handleOrderRejected" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElImage, ElTimeline, ElTimelineItem } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import API from '@/api/maintainance'
import HMultiUpload from '@/components/HMultiUpload/index.vue'
import CloseOrderDialog from './components/CloseOrderDialog.vue'
import RejectOrderDialog from './components/RejectOrderDialog.vue'
import { useDictStore } from '@/stores/modules/dict'
import _D from '@/edata/_osp_data'

const route = useRoute()
const router = useRouter()
const dictStore = useDictStore();

const closeOrderDialogVisible = ref(false);
const rejectOrderDialogVisible = ref(false);
const action = route.query.action

const pmSpecialFlag = _D.pmSpecialFlag;
const businessType = _D.businessType;
const isWarranty = _D.isWarranty;
const identityType = _D.identityType;

const isSubCenter = ref(null);
const activeTabName = ref('processInfo')
const editable = computed(() => {
  return ['TO_PROCESS'].includes(formData.value.orderStatus) && action !== 'view';
})

const getDictLabel = (dictType, value) => {
  const dictMap = dictStore.getDictMapByType(dictType);
  return dictMap[value] || '-';
};

// 表单数据 - 包含截图中的所有字段（使用假设的字段名）
const formData = ref({
  address: '',
  auditMode: '',
  auditUnit: '',
  businessType: '',
  cityId: '',
  cityName: '',
  closeReason: '',
  closeTime: '',
  configCheckItems: [],
  configId: '',
  createdAt: '',
  createdBy: '',
  deadline: '',
  devices: {},
  dispatchAuditPermission: '',
  dispatchMode: '',
  dispatchUnit: '',
  dispatched: null,
  faultDescription: '',
  faultInfos: [],
  finishFlag: '',
  finishTime: '',
  firstAuditResult: '',
  handleCheckItems: [],
  handleTime: '',
  handler: '',
  id: '',
  inverterSn: '',
  isWarranty: null,
  opCode: '',
  opMemberId: '',
  opName: '',
  opType: '',
  orderCode: '',
  orderName: '',
  orderSource: '',
  orderStatus: '',
  orderType: '',
  overTime: null,
  processes: [],
  provinceId: '',
  provinceName: '',
  regionId: '',
  regionName: '',
  remark: '',
  secondAuditResult: '',
  sparePartApplyNo: '',
  sparePartAuditPassTime: '',
  sparePartAuditStatus: '',
  specialFlag: '',
  stationCode: '',
  stationMode: '',
  stationName: '',
  stationPhone: '',
  streetId: '',
  streetName: '',
  subCenterName: '',
})

// 获取详情数据
const getDetail = async () => {
  const orderCode = route.query.orderCode
  if (!orderCode) {
    ElMessage.error('缺少工单标识')
    router.back()
    return
  }
  formData.value.orderCode = orderCode

  try {
    const res = await API.getWorkOrderByOrderCode(orderCode)

    if (res.success) {
      Object.assign(formData.value, res.result)
      if (!['TO_HEAD_DISPATCH', 'TO_SUB_CENTER_DISPATCH', 'TO_ASSIGN', 'CLOSED'].includes(formData.value.orderStatus) && action !== 'view') {
        activeTabName.value = 'workOrderHandle';
      } else {
        activeTabName.value = 'processInfo';
      }

      if (Array.isArray(formData.value.handleCheckItems) && formData.value.handleCheckItems.length === 0) {
        formData.value.handleCheckItems = formData.value.configCheckItems?.map(item => ({ ...item, resultContent: null })) || [];
      }

      if (formData.value.createdAt) {
        formData.value.createdAt = formData.value.createdAt.replace('T', ' ')
      }
    } else {
      ElMessage.error(res.error || '获取详情失败')
    }
  } catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error('获取详情数据时发生错误')
  }
}

const formRef = ref(null);

// 提交处理
const handleSubmit = async () => {
  if (!formRef.value) return;

  formRef.value.validate(async (valid) => {
    if (!valid) {
      ElMessage.error('请完成所有必填项');
      return;
    }

    ElMessageBox.confirm('确认提交工单处理结果吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(async () => {
      const params = {
        orderCode: formData.value.orderCode,
        remark: formData.value.remark,
        handleCheckItems: formData.value.handleCheckItems?.map(item => {
          if (item.resultType === 'image' && Array.isArray(item.resultContent)) {
            return {
              ...item,
              resultContent: item.resultContent.join(',')
            }
          }
          return item;
        }) || []
      }

      try {
        const res = await API.handleWorkOrder(params)

        if (res.data.success) {
          ElMessage.success('提交成功')
          router.back()
        } else {
          ElMessage.error(res.data.error || '提交失败')
        }
      } catch (error) {
        console.error('提交失败:', error)
        ElMessage.error('提交处理结果时发生错误')
      }
    }).catch(() => {
      // 取消提交
    });
  });
}

// 下发工单
const dispatchOrder = async () => {
  ElMessageBox.confirm('确认下发工单?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      const res = await API.dispatchWorkOrder(formData.value.orderCode)

      if (res.data.success) {
        ElMessage.success('提交成功')
        router.back()
      } else {
        ElMessage.error(res.data.error || '提交失败')
      }
    } catch (error) {
      console.error('提交失败:', error)
      ElMessage.error('提交处理结果时发生错误')
    }
  }).catch(() => {
    // 取消提交
  });
}

// 审核通过
const handleAuditPass = async () => {
  ElMessageBox.confirm('确认审核通过吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      const res = await API.auditPassWorkOrder(formData.value.orderCode) // 假设接口需要工单ID

      if (res.data.success) {
        ElMessage.success('审核通过成功')
        router.back();
      } else {
        ElMessage.error(res.data.error || '审核通过失败')
      }
    } catch (error) {
      console.error('审核通过失败:', error)
      ElMessage.error('审核通过时发生错误')
    }
  }).catch(() => {
    // 取消审核
  });
}

// 处理打开关单弹窗
const handleOpenCloseOrderDialog = () => {
  closeOrderDialogVisible.value = true;
};

// 处理打开驳回弹窗
const handleOpenRejectOrderDialog = () => {
  rejectOrderDialogVisible.value = true;
};

// 工单成功驳回后的回调
const handleOrderRejected = () => {
  router.back(); // 返回上一页
};

// 工单成功关闭后的回调
const handleOrderClosed = () => {
  router.back(); // 返回上一页
};

const checkIsSubCenter = () => {
  API.getIsSubCenterUser()
    .then((res) => {
      isSubCenter.value = res.result;
    })
}

// 组件挂载后获取数据
onMounted(() => {
  getDetail()
  checkIsSubCenter();
  dictStore.fetchDict([
    'fault_level',
    'work_order_type',
    'work_order_status',
    'dispatch_mode',
    'dispatch_review_permission',
    'review_mode',
    'work_order_source',
    'close_order_reason',
    'audit_reject_reason'
  ]);
})

</script>

<style lang="less" scoped>
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_view.less';

.fault-detail-page {
  padding: 20px;
  background-color: #f8f9fa;
  height: 100%;
  box-sizing: border-box;
  overflow: auto;

  .section-card {
    margin-bottom: 20px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    .card-header {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    :deep(.el-card__header) {
      background-color: #ffffff;
      padding: 12px 20px;
      border-bottom: 1px solid #e9ecef;
    }

    :deep(.el-card__body) {
      padding: 20px;
    }

    // Descriptions 样式调整
    :deep(.el-descriptions__label) {
      font-weight: normal;
      color: #6c757d;
      background-color: #f8f9fa;
    }

    :deep(.el-descriptions__content) {
      color: #212529;
    }

    :deep(.el-descriptions__cell) {
      padding: 8px 12px;
    }
  }

  .info-label {
    color: #6c757d;
    margin-bottom: 8px;
    font-size: 14px;
  }

  .image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    color: #c0c4cc;
    font-size: 14px;
  }

  // 工单处理表单样式
  .el-form {
    :deep(.el-form-item__label) {
      font-weight: 600;
      color: #495057;
      padding-bottom: 4px;
    }


    :deep(.el-upload--picture-card) {
      width: 150px;
      height: 150px;
    }

    :deep(.el-upload-list--picture-card .el-upload-list__item) {
      width: 150px;
      height: 150px;
    }
  }


  .action-buttons {
    margin-top: 20px;
    padding: 15px 20px;
    background-color: #ffffff;
    border-top: 1px solid #e9ecef;
    text-align: right;
    position: sticky;
    bottom: 0;
    z-index: 10;
  }
}

.handle-check-items {
  margin-top: 10px;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;

  .check-items-title {
    font-weight: bold;
    margin-bottom: 8px;
    color: #409EFF;
  }
}
</style>
