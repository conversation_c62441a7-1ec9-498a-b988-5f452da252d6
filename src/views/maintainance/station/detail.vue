<template>
  <div class="station-detail-page">
    <el-page-header class="cus-back" icon="ArrowLeft" @back="$router.go(-1)" />

    <!-- 电站信息 -->
    <el-card class="section-card">
      <template #header>
        <div class="card-header">
          <span>电站信息</span>
        </div>
      </template>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="电站编码">{{ formData.stationCode || '-' }}</el-descriptions-item>
        <el-descriptions-item label="逆变器SN码">
          <el-button v-if="formData.inverterSn" link type="primary">
            {{ formData.inverterSn }}
          </el-button>
        </el-descriptions-item>
        <el-descriptions-item label="业主姓名">{{ formData.stationName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ formData.stationPhone || '-' }}</el-descriptions-item>
        <el-descriptions-item label="电站模式">{{ formData.stationMode || '-' }}</el-descriptions-item>
        <el-descriptions-item label="关联资方">{{ pmSpecialFlag[formData.specialFlag] || '-' }}</el-descriptions-item>
        <el-descriptions-item label="所属分中心">{{ formData.subCenterName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="服务商类别">{{ identityType[formData.opType] || '-' }}</el-descriptions-item>
        <el-descriptions-item label="运维商名称">{{ formData.opName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="运维商业务类型">{{ businessType[formData.businessType] || '-' }}</el-descriptions-item>
        <el-descriptions-item label="是否质保期内">{{ isWarranty[formData.isWarranty] || '-' }}</el-descriptions-item>
        <el-descriptions-item label="区域">{{ (formData.provinceName || '') + (formData.cityName || '') +
          (formData.regionName
            || '') || '-' }}</el-descriptions-item>
        <el-descriptions-item label="详细地址">{{ formData.address || '-' }}</el-descriptions-item>
      </el-descriptions>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import API from '@/api/maintainance'
import _D from '@/edata/_osp_data'

const route = useRoute()
const router = useRouter()

const pmSpecialFlag = _D.pmSpecialFlag;
const businessType = _D.businessType;
const isWarranty = _D.isWarranty;
const identityType = _D.identityType;

const formData = ref({
  address: '',
  auditMode: '',
  auditUnit: '',
  businessType: '',
  cityId: '',
  cityName: '',
  closeReason: '',
  closeTime: '',
  configCheckItems: [],
  configId: '',
  createdAt: '',
  createdBy: '',
  deadline: '',
  devices: {},
  dispatchAuditPermission: '',
  dispatchMode: '',
  dispatchUnit: '',
  dispatched: null,
  faultDescription: '',
  faultInfos: [],
  finishFlag: '',
  finishTime: '',
  firstAuditResult: '',
  handleCheckItems: [],
  handleTime: '',
  handler: '',
  id: '',
  inverterSn: '',
  isWarranty: null,
  opCode: '',
  opMemberId: '',
  opName: '',
  opType: '',
  orderCode: '',
  orderName: '',
  orderSource: '',
  orderStatus: '',
  orderType: '',
  overTime: null,
  processes: [],
  provinceId: '',
  provinceName: '',
  regionId: '',
  regionName: '',
  remark: '',
  secondAuditResult: '',
  sparePartApplyNo: '',
  sparePartAuditPassTime: '',
  sparePartAuditStatus: '',
  specialFlag: '',
  stationCode: '',
  stationMode: '',
  stationName: '',
  stationPhone: '',
  streetId: '',
  streetName: '',
  subCenterName: '',
})

const getDetail = async () => {
  const orderCode = route.query.orderCode
  if (!orderCode) {
    ElMessage.error('缺少工单标识')
    router.back()
    return
  }
  formData.value.orderCode = orderCode

  try {
    const res = await API.getWorkOrderByOrderCode(orderCode)

    if (res.success) {
      Object.assign(formData.value, res.result)

      if (Array.isArray(formData.value.handleCheckItems) && formData.value.handleCheckItems.length === 0) {
        formData.value.handleCheckItems = formData.value.configCheckItems?.map(item => ({ ...item, resultContent: null })) || [];
      }

      if (formData.value.createdAt) {
        formData.value.createdAt = formData.value.createdAt.replace('T', ' ')
      }
    } else {
      ElMessage.error(res.error || '获取详情失败')
    }
  } catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error('获取详情数据时发生错误')
  }
}

onMounted(() => {
  getDetail()
})

</script>

<style lang="less" scoped>
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_view.less';

.station-detail-page {
  padding: 20px;
  background-color: #f8f9fa;
  height: 100%;
  box-sizing: border-box;
  overflow: auto;

  .section-card {
    margin-bottom: 20px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    .card-header {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    :deep(.el-card__header) {
      background-color: #ffffff;
      padding: 12px 20px;
      border-bottom: 1px solid #e9ecef;
    }

    :deep(.el-card__body) {
      padding: 20px;
    }

    // Descriptions 样式调整
    :deep(.el-descriptions__label) {
      font-weight: normal;
      color: #6c757d;
      background-color: #f8f9fa;
    }

    :deep(.el-descriptions__content) {
      color: #212529;
    }

    :deep(.el-descriptions__cell) {
      padding: 8px 12px;
    }
  }

  .info-label {
    color: #6c757d;
    margin-bottom: 8px;
    font-size: 14px;
  }

  .image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    color: #c0c4cc;
    font-size: 14px;
  }
}
</style>
